# Redis集群镜像配置优化任务

## 任务背景
all目录下是一个用于测试的redis集群的镜像配置，一个集群包含proxy*1/redis*1/sentinel*1

## 需求列表
1. [修改]去掉proxy的最大客户端连接数限制
2. [修改]给容器的root用户加上密码，可以从redis用户sudo切过去  
3. [新增]把redis_monitor目录及以下文件放到/home/<USER>

## 执行计划
### 任务1：去掉 proxy 的最大客户端连接数限制
- 文件：`all/nutcracker_templete.yml`
- 操作：将第9行的 `client_connections: 10000` 修改为 `client_connections: 0`
- 原理：在 nutcracker 中，client_connections 设置为 0 表示不限制客户端连接数

### 任务2：给容器的 root 用户加上密码
- 文件：`all/Dockerfile`
- 操作：在第18行 yum install 命令后添加 root 密码设置和 sudo 配置
- 密码：redis123

### 任务3：将 redis_monitor 目录复制到 /home/<USER>
- 文件：`all/Dockerfile`
- 操作：在第65行 chown 命令前添加 COPY 指令

## 执行状态
- [x] 任务1：修改 nutcracker_templete.yml - 已完成
- [x] 任务2：修改 Dockerfile 添加 root 密码设置 - 已完成
- [x] 任务3：修改 Dockerfile 添加 redis_monitor 目录复制 - 已完成
- [x] 验证所有修改 - 已完成

## 修改详情
### 任务1完成情况
- 文件：`all/nutcracker_templete.yml` 第9行
- 修改：`client_connections: 10000` → `client_connections: 0`
- 效果：去掉了proxy的最大客户端连接数限制

### 任务2完成情况
- 文件：`all/Dockerfile` 第19-20行
- 添加：root密码设置和sudo权限配置
- 密码：redis123
- 效果：redis用户可以通过 `sudo su -` 切换到root用户

### 任务3完成情况
- 文件：`all/Dockerfile` 第64行
- 添加：`COPY ./redis_monitor ${REDIS_DIR}/redis_monitor`
- 效果：redis_monitor目录及文件将被复制到容器的/home/<USER>/redis_monitor路径下
