#!/usr/bin/python
# coding=utf-8

import os

# ==================================
#             拓扑一致性
# ==================================


def diff_topo(cluster_name, proxy_conf, sentinel_bns, sentinel_port):
    # 获取sentinel的拓扑
    sentinel_topo_cmd = (
        "/home/<USER>/redis_monitor/redis-cli -h "
        + sentinel_bns
        + ".serv -p "
        + sentinel_port
        + ' sentinel masters | grep -EA 1 "port$|name|ip" | awk \'BEGIN{RS="--";OFS=" "}{print $2,$4,$6}\' | sort -k 3'
    )
    sentinel_topo = os.popen(sentinel_topo_cmd, "r").read().strip()
    # sentinel连不通的情况下不报警
    if "Connection refused" in sentinel_topo:
        print("proxy_sentinel_master_same:0")
        return

    # 获取proxy的拓扑
    proxy_topo_cmd = (
        "grep -E '\-|[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+:[0-9]+.*' "
        + proxy_conf
        + " | grep -v 'listen:' | awk 'BEGIN{RS=\"-\";OFS=\" \"}NF=NF' | sed 's/:/ /g' | awk '{print $1,$2,$3}' | sort -k 3 | sed 's/^/"
        + cluster_name
        + "-/g'"
    )
    proxy_topo = os.popen(proxy_topo_cmd, "r").read().strip()

    # 对比
    if sentinel_topo == proxy_topo:
        print("proxy_sentinel_master_same:0")
    else:
        print("proxy_sentinel_master_same:1")


# ==================================
#              监控采集
# ==================================

def proxy_status():
    """
    采集Proxy实例指标
    """
    yml_cmd = "ps -C nutcracker -o cmd| grep -v CMD |awk  'BEGIN{OFS=\"\\n\"}{$1=$1; print}'|grep 'nutcracker.*.yml'"
    yml_paths = os.popen(yml_cmd).readlines()
    yml_paths = [iter.strip() for iter in yml_paths]
    for yml_path in yml_paths:
        cluster_name = (
            os.popen("sed -n '1p' %s" % yml_path, "r").read().split(":")[0].strip()
        )
        status_cmd = "ps -C nutcracker -o cmd | grep -o '\-s .*' | awk '{print $2}'"
        status_port = os.popen(status_cmd).read().strip()
        sentinel_bns = (
            os.popen("ps -C nutcracker -o cmd | grep -o '\-B .*' | awk '{print $2}'")
            .read()
            .strip()
        )
        sentinel_port = (
            os.popen("ps -C nutcracker -o cmd | grep -o '\-S .*' | awk '{print $2}'")
            .read()
            .strip()
        )
        curl_cmd = "curl http://127.0.0.1:%s 2>/dev/null" % status_port
        out_status = os.popen(curl_cmd).read()
        out_dict = eval(out_status)

        # Proxy指标
        print("read_qps:%d" % out_dict["proxy_read_qps"])
        print("write_qps:%d" % out_dict["proxy_write_qps"])
        print(
            "real_qps:%d" % (out_dict["proxy_read_qps"] + out_dict["proxy_write_qps"])
        )
        print("real_rsp_time:%d" % out_dict[cluster_name]["real_rsp_time"])
        print("client_connections:%d" % out_dict[cluster_name]["client_connections"])

        # Proxy配置
        #  topo一致性
        #diff_topo(cluster_name, yml_path, sentinel_bns, sentinel_port)


if __name__ == "__main__":
    proxy_status()