FROM r.duxiaoman-int.com/dxm_base/base:bbc_res_20250312194246
ARG SOFT_INSTALL_DIR="/home/<USER>/local/install"
ARG SOFT_REDIS_DIR="/home/<USER>/local/redis"
ARG SOFT_ROUTER_DIR="/home/<USER>/local/router"
ARG SOFT_SENTINEL_DIR="/home/<USER>/local/sentinel"
ARG SOFT_DIR="/home/<USER>"
ARG REDIS_DIR="/home/<USER>"
MAINTAINER huzhaoyun_dxm

USER root
ARG UNAME=redis
ARG UID=1200
ARG GID=1201
ENV INIT_NOAH_USER=redis
ENV INIT_ENTRYPOINT_USER=redis
RUN groupadd -g $GID -o $UNAME && \
    useradd -m -u $UID -g $GID -o -s /bin/bash $UNAME
RUN yum install -y -q vim sudo strace psmisc lsof net-tools less lrzsz jq zip unzip telnet tcpdump && yum clean all
RUN echo 'root:redis123' | chpasswd && \
    echo 'redis ALL=(ALL) ALL' >> /etc/sudoers
WORKDIR ${REDIS_DIR}
RUN mkdir ${SOFT_INSTALL_DIR} -pv
COPY ./centos-7.repo /etc/yum.repos.d/centos-7.repo
COPY ./entrypoint.sh ${SOFT_INSTALL_DIR}

# redis
RUN mkdir ${SOFT_REDIS_DIR}/var -pv && \
    cd ${SOFT_REDIS_DIR} && \
    wget http://irep.build.duxiaoman-int.com/product/v3/download/release/dxm/dba/redis-server/*******/output.tgz && \
    tar zxf output.tgz && \
    mv output/bin ./ && \
    rm -rf output* && \
    rm irepo-meta.txt

COPY ./redis_template.conf ${SOFT_INSTALL_DIR}
COPY ./redis_service.sh ${SOFT_REDIS_DIR}
RUN chmod +x ${SOFT_REDIS_DIR}/*.sh

# sentinel
RUN mkdir ${SOFT_SENTINEL_DIR} -pv && \
    cd ${SOFT_SENTINEL_DIR} && \
    wget http://irep.build.duxiaoman-int.com/product/v3/download/release/dxm/dba/redis-server/*******/output.tgz &&\
    tar zxf output.tgz &&\
    mv output/bin ./ &&\
    rm -rf output* &&\
    rm irepo-meta.txt
COPY ./sentinel_template.conf ${SOFT_INSTALL_DIR}
COPY ./sentinel_service.sh ${SOFT_SENTINEL_DIR}
RUN chmod +x ${SOFT_SENTINEL_DIR}/*.sh

# router
RUN mkdir ${SOFT_ROUTER_DIR}/var -pv &&\
    cd ${SOFT_ROUTER_DIR} &&\
    wget http://irep.build.duxiaoman-int.com/product/v3/download/release/dxm/dba/router-server/2.1.1.3/output.tgz &&\
    tar zxf output.tgz &&\
    mv output/bin ./ &&\
    rm -rf output* &&\
    rm irepo-meta.txt
COPY ./nutcracker_templete.yml ${SOFT_INSTALL_DIR}
COPY ./nutcracker_service.sh ${SOFT_ROUTER_DIR}
RUN chmod +x ${SOFT_ROUTER_DIR}/*.sh

COPY ./version ${SOFT_REDIS_DIR}
COPY ./redis_monitor ${REDIS_DIR}/redis_monitor

RUN chmod +x ${SOFT_INSTALL_DIR}/*.sh ${SOFT_ROUTER_DIR}/*.sh &&\
    chmod +x ${REDIS_DIR}/redis_monitor/redis-cli &&\
    mkdir ${SOFT_DIR}/coresave && ln -s ${REDIS_DIR}/coresave /home/<USER>
    mkdir ${REDIS_DIR}/local && chown redis:redis -R ${SOFT_DIR} /home/<USER>

USER redis
ENTRYPOINT [ "sh", "-c", "/home/<USER>/local/install/entrypoint.sh run"]