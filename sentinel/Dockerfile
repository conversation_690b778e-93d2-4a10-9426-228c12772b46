FROM r.duxiaoman-int.com/dxm_base/base:bbc_res_20250312194246
ARG SOFT_INSTALL_DIR="/home/<USER>/local/install"
ARG SOFT_SENTINEL_DIR="/home/<USER>/local/sentinel"
ARG SOFT_DIR="/home/<USER>"
ARG REDIS_DIR="/home/<USER>"

MAINTAINER huzhaoyun_dxm

USER root
ARG UNAME=redis
ARG UID=1200
ARG GID=1201
ENV INIT_NOAH_USER=redis
ENV INIT_ENTRYPOINT_USER=redis
RUN groupadd -g $GID -o $UNAME \
    && useradd -m -u $UID -g $GID -o -s /bin/bash $UNAME
    RUN  yum install -y -q vim sudo strace psmisc lsof net-tools less lrzsz jq zip unzip telnet tcpdump && yum clean all
WORKDIR ${REDIS_DIR}
RUN mkdir ${SOFT_SENTINEL_DIR} -pv \
    && mkdir ${SOFT_INSTALL_DIR} -pv \
    && cd ${SOFT_SENTINEL_DIR} \
    && wget http://irep.build.duxiaoman-int.com/product/v3/download/release/dxm/dba/redis-server/*******/output.tgz \
    && tar zxf output.tgz \
    && mv output/bin ./ \
    && rm -rf output* \
    && rm irepo-meta.txt
COPY ./sentinel_template.conf ${SOFT_INSTALL_DIR}
COPY ./sentinel-entrypoint.sh ${SOFT_INSTALL_DIR}
COPY ./sentinel_service.sh ${SOFT_SENTINEL_DIR}
COPY ./centos-7.repo /etc/yum.repos.d/centos-7.repo
COPY ./version ${SOFT_REDIS_DIR}
RUN chmod +x ${SOFT_INSTALL_DIR}/*.sh ${SOFT_SENTINEL_DIR}/*.sh \
    && mkdir ${SOFT_DIR}/coresave/ && ln -s ${REDIS_DIR}/coresave /home/<USER>
    && mkdir ${REDIS_DIR}/local && chown redis:redis -R ${SOFT_DIR} /home/<USER>

ENTRYPOINT [ "base_init", "/home/<USER>/local/install/sentinel-entrypoint.sh run"] 