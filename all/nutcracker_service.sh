#!/bin/bash
# 用于启动router 的shell脚本.
# author: nan<PERSON><PERSON><PERSON><PERSON>@duxiaoman.com 2023.03.09

readonly APP_HOME="$(
    cd "$(dirname "$0")"
    pwd
)"
readonly sentinel_bns=SENTINEL_BNS
readonly sentinel_port=SENTINEL_PORT
readonly status_port=STAT_PORT

# app的bin文件名，需要放置在${APP_HOME}/bin/目录下
bin_name="nutcracker"
bin_file="${APP_HOME}/bin/${bin_name}"
# pid的绝对路径
pid_file="${APP_HOME}/var/${bin_name}.pid"
config_file="${APP_HOME}/conf/${bin_name}.yml"
log_file="${APP_HOME}/log/${bin_name}.log"
whitelist="/home/<USER>/whitelist/whitelist"
# pid的name
pid_bin_name=""

# 通过pid file 或 ps 命令获取pid
function get_pid_online() {
    if [[ -e ${pid_file} ]] && pid_bin_name=$(cat ${pid_file}) && [[ "${pid_bin_name}" != "" ]]; then
        echo "[${bin_name} pid get from file](pid=${pid_bin_name})"
    else
        get_app_pid_by_psef
    fi
}
# 通过ps命令获取pid
function get_app_pid_by_psef() {
    pid_bin_name="$(ps -ef | grep ${APP_HOME}/bin/${bin_name} | grep ${config_file} | grep -v grep | awk '{print $2}' | head -n 1)"
}
# 启动进程
function start() {
    get_app_pid_by_psef

    # 1.判断进程是否已经启动
    if [[ ! -z "${pid_bin_name}" ]]; then
        echo "[${bin_name} already started!](pid=${pid_bin_name})"
        exit 0
    fi

    # 2.启动proxy实例
    ${bin_file} -c ${config_file} -o ${log_file} -d -B ${sentinel_bns} -S ${sentinel_port} -s ${status_port} -v 4 -m 16384 -w ${whitelist} -p ${pid_file}
    sleep 1

    # 3.通过ps -ef查找app的pid，检查启动是否成功
    get_app_pid_by_psef
    if [[ ! -z "${pid_bin_name}" ]] && [[ -e ${pid_file} ]]; then
        current_pid=${pid_bin_name}
        get_app_pid_by_psef
        sleep 1
        if [ "${pid_bin_name}" == "${current_pid}" ]; then
            echo "[${bin_name} start success!] (pid=${pid_bin_name})"
            exit 0
        else
            echo "[${bin_name} start failed! progress id was changed]"
            exit 1
        fi

    else
        echo "[${bin_name} start failed!] (could not found ${bin_name} progress)"
        exit 1
    fi
}
# 关闭进程
function stop() {
    # 1.通过pid文件或者ps -ef方式获取pid
    get_pid_online
    stop_bin_pid=${pid_bin_name}
    # 2.判断pid是否存在并进行kill操作
    if [[ -z "${pid_bin_name}" ]]; then
        echo "[${bin_name} has been stoped already](pid=${stop_bin_pid})"
    else
        kill -2 ${pid_bin_name}
        sleep 2
        get_app_pid_by_psef
        if [[ "${pid_bin_name}" == "" ]]; then
            echo "[${bin_name} stop success!](pid=${stop_bin_pid})"
        else
            echo "[${bin_name} stop failed!](pid=${pid_bin_name})"
        fi
    fi
}

#状态
function status() {
    get_app_pid_by_psef
    if [[ "${pid_bin_name}" != "" ]]; then
        echo -e "\033[32m [${bin_name} is running!](pid=${pid_bin_name}) \033[0m"
    else
        echo -e "\033[31m [${bin_name} is not running!](pid=${pid_bin_name}) \033[0m"
    fi
}

# usage
function usage() {
    echo "Usage: $0 {start|stop|status|restart}"
}

function main() {
    case "$1" in
    'start')
        start
        ;;
    'stop')
        stop
        ;;
    'restart')
        stop
        start
        ;;
    'status')
        status
        ;;
    '-h')
        usage
        ;;
    *)
        usage
        exit 1
        ;;
    esac
    exit 0
}

# 7. 主函数/主逻辑
main "$@"
