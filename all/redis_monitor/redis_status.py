#!/usr/bin/python
# coding=utf-8

import os
import stat
import commands
import time


#
monitor_dir = "/home/<USER>/redis_monitor"
redis_cli = os.path.join(monitor_dir, "redis-cli")
redis_conf_dir = "/home/<USER>/local/redis/conf/redis.conf"
port = int(
    os.popen("grep ^port %s | awk '{print $2}'" % redis_conf_dir, "r").read().strip()
)
tmp_file = os.path.join(monitor_dir, ".redis_info_status_%d.tmp" % port)


# ==================================
#               慢查
# ==================================


def analyze_slow(port):
    """
    检查最近10s内有无慢查记录
    """
    redis_cli = "/home/<USER>/redis_monitor/redis-cli"
    output = commands.getoutput(
        "%s --no-raw -p %d slowlog get 32" % (redis_cli, port)
    ).split("\n")

    # 当前时间
    now_t = time.time()

    counter = 0
    for line in output:
        if line.startswith("    2) (integer)"):
            # 将时间戳转换为日期时间，精确到毫秒
            timestamp = line.split(" ")[-1]
            # 慢查记录是按时间戳倒排的，如果当前的时间戳早于10s前，后面的都不用看了
            if now_t - 10 <= int(timestamp):
                counter += 1
            else:
                break

    print("slow:%d" % counter)


# ==================================
#           redis status
# ==================================


def get_prev_status(redis_incr_status):
    """
    获取上一个周期的值
    """
    if os.path.isfile(tmp_file) == True:
        status_lines = open(tmp_file, "r").readlines()
        for line in status_lines:
            (key, value) = line.split(" ")
            redis_incr_status[key] = int(value)
    return redis_incr_status


def write_status(redis_incr_status):
    """
    写到临时文件
    """
    status_file = open(tmp_file, "w")
    for key, value in redis_incr_status.items():
        status_file.write("%s %s\n" % (key, value))
    status_file.close()
    os.chmod(tmp_file, stat.S_IRWXU + stat.S_IRWXG + stat.S_IRWXO)


def get_new_status(redis_prev_status):
    redis_int_status = {
        "connected_clients": 0,
        "blocked_clients": 0,
        "client_longest_output_list": 0,
        "client_biggest_input_buf": 0,
        "maxmemory": 0,
        "used_memory": 0,
        "used_memory_rss": 0,
        "used_memory_peak": 0,
        "used_memory_lua": 0,
        "used_memory_dataset": 0,
        "aof_enabled": 0,
        "aof_last_rewrite_time_sec": 0,
        "latest_fork_usec": 0,
        "connected_slaves": 0,
        "repl_backlog_size": 0,
        "instantaneous_ops_per_sec": 0,
    }

    redis_float_status = {
        "instantaneous_input_kbps": 0,
        "instantaneous_output_kbps": 0,
        "mem_fragmentation_ratio": 0,
    }

    redis_str_status = {
        "aof_last_bgrewrite_status": "",
        "aof_last_write_status": "",
        "role": "",
        "master_link_status": "",
    }

    redis_incr_status = {
        "total_connections_received": -1,
        "total_commands_processed": -1,
        "rejected_connections": -1,
        "expired_keys": -1,
        "evicted_keys": -1,
        "keyspace_hits": -1,
        "keyspace_misses": -1,
    }

    redis_info = os.popen(
        "%s -p %d info | grep -v '^#'|grep -v '^$'|sed 's/^[ \t]*//g'|sed 's/[ \t]*$//g'"
        % (redis_cli, port)
    ).readlines()

    redis_status = {}
    for line in redis_info:
        line = line.strip().lower()
        if line.startswith("#") or len(line) <= 1:
            continue

        (key, value) = line.split(":")
        if key in redis_int_status:
            redis_status[key] = int(value)
            print("%s:%d" % (key, redis_status[key]))
        if key in redis_float_status:
            redis_status[key] = float(value)
            print("%s:%.2f" % (key, redis_status[key]))
        if key in redis_str_status:
            redis_status[key] = str(value)
            print('%s:"%s"' % (key, redis_status[key]))
        if key in redis_incr_status:
            redis_status[key] = int(value)
            redis_incr_status[key] = redis_status[key]
            redis_status["%s_df" % key] = redis_status[key] - redis_prev_status[key]
            print("%s:%d" % (key, redis_status[key]))
            print("%s_df:%s" % (key, redis_status["%s_df" % key]))
        if key == "db0":
            redis_status["total_keys"] = int(value.split(",")[0].split("=")[1])
            print("total_keys:%d" % (redis_status["total_keys"]))
            redis_status["expire_keys"] = int(line.split(",")[1].split("=")[1])
            print("expire_keys:%d" % (redis_status["expire_keys"]))
  
    return redis_status, redis_incr_status


if __name__ == "__main__":
    prev_status = get_prev_status(
        {
            "total_connections_received": -1,
            "total_commands_processed": -1,
            "rejected_connections": -1,
            "expired_keys": -1,
            "evicted_keys": -1,
            "keyspace_hits": -1,
            "keyspace_misses": -1,
        }
    )
    redis_status, redis_incr_status = get_new_status(prev_status)
    write_status(redis_incr_status)
    analyze_slow(port)
