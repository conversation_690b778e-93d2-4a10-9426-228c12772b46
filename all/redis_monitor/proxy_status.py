#!/usr/bin/python
# coding=utf-8

import os

# ==================================
#              监控采集
# ==================================

def proxy_status():
    """
    采集Proxy实例指标
    """
    yml_cmd = "ps -C nutcracker -o cmd| grep -v CMD |awk  'BEGIN{OFS=\"\\n\"}{$1=$1; print}'|grep 'nutcracker.*.yml'"
    yml_paths = os.popen(yml_cmd).readlines()
    yml_paths = [iter.strip() for iter in yml_paths]
    for yml_path in yml_paths:
        cluster_name = (
            os.popen("sed -n '1p' %s" % yml_path, "r").read().split(":")[0].strip()
        )
        status_cmd = "ps -C nutcracker -o cmd | grep -o '\-s .*' | awk '{print $2}'"
        status_port = os.popen(status_cmd).read().strip()

        curl_cmd = "curl http://127.0.0.1:%s 2>/dev/null" % status_port
        out_status = os.popen(curl_cmd).read()
        out_dict = eval(out_status)

        # Proxy指标
        print("read_qps:%d" % out_dict["proxy_read_qps"])
        print("write_qps:%d" % out_dict["proxy_write_qps"])
        print(
            "real_qps:%d" % (out_dict["proxy_read_qps"] + out_dict["proxy_write_qps"])
        )
        print("real_rsp_time:%d" % out_dict[cluster_name]["real_rsp_time"])
        print("client_connections:%d" % out_dict[cluster_name]["client_connections"])


if __name__ == "__main__":
    proxy_status()