#!/bin/bash
set -x

pid=0
readonly REDIS_DIR="/home/<USER>"
readonly REDIS_HOME="/home/<USER>/local/redis"
readonly SENTINEL_HOME="/home/<USER>/local/sentinel"
readonly ROUTER_HOME="/home/<USER>/local/router"
readonly APP_INSTALL="/home/<USER>/local/install"
readonly SOFT_DIR="/home/<USER>"
readonly START_SHELL="${ROUTER_HOME}/nutcracker_service.sh"

# REDIS_PORT
if [ -z "$REDIS_PORT" ]; then
    # 如果REDIS_PORT环境变量不存在，则使用默认值
    REDIS_PORT="7000"
fi
# MAX_MEMORY_POLICY
if [ -z "$MAX_MEMORY_POLICY" ]; then
    # 如果MAX_MEMORY_POLICY环境变量不存在，则使用默认值
    MAX_MEMORY_POLICY="volatile-lru"
fi

# SENTINEL_PORT
if [ -z "$SENTINEL_PORT" ]; then
    # 如果SENTINEL_PORT环境变量不存在，则使用默认值
    SENTINEL_PORT="9000"
fi

# ROUTER_PORT
if [ -z "$ROUTER_PORT" ]; then
    # 如果ROUTER_PORT环境变量不存在，则使用默认值
    ROUTER_PORT="8000"
fi

# STAT_PORT
if [ -z "$STAT_PORT" ]; then
    # 如果STAT_PORT环境变量不存在，则使用默认值
    STAT_PORT="10000"
fi

# 替换必要的配置
conf_sed() {
    cp ${APP_INSTALL}/redis_template.conf ${REDIS_HOME}/conf/redis.conf
    sed -i "s/REDIS_PORT/${REDIS_PORT}/g" ${REDIS_HOME}/conf/redis.conf
    sed -i "s/MAX_MEMORY_POLICY/${MAX_MEMORY_POLICY}/g" ${REDIS_HOME}/conf/redis.conf

    cp ${APP_INSTALL}/sentinel_template.conf ${SENTINEL_HOME}/conf/sentinel.conf
    sed -i "s/SENTINEL_PORT/${SENTINEL_PORT}/g" ${SENTINEL_HOME}/conf/sentinel.conf

    cp ${APP_INSTALL}/nutcracker_templete.yml ${ROUTER_HOME}/conf/nutcracker.yml
    sed -i "s/ROUTER_PORT/${ROUTER_PORT}/g" ${ROUTER_HOME}/conf/nutcracker.yml
    sed -i "s/REDIS_PORT/${REDIS_PORT}/g" ${ROUTER_HOME}/conf/nutcracker.yml
    sed -i "s/SENTINEL_PORT/${SENTINEL_PORT}/g" ${START_SHELL}
    sed -i "s/STAT_PORT/${STAT_PORT}/g" ${START_SHELL}
    client_auth=""
    if [ ! -z "${CLIENT_AUTH}" ]; then
        client_auth="client_auth: '${CLIENT_AUTH}'"
    fi
    sed -i "s/CLIENT_AUTH/${client_auth}/g" ${ROUTER_HOME}/conf/nutcracker.yml
}

init_env() {
    # redis
    # 移动bin过去
    mkdir -p ${REDIS_DIR}/local/redis/bin/ && mv ${SOFT_DIR}/local/redis/bin/* ${REDIS_DIR}/local/redis/bin/
    # 替换启动脚本
    mv ${SOFT_DIR}/local/redis/redis_service.sh ${REDIS_DIR}/local/redis/
    # 生成白名单目录
    mkdir -p ${REDIS_HOME}/whitelist && touch ${REDIS_HOME}/whitelist/whitelist.ip && touch ${REDIS_HOME}/whitelist/whitelist.bns
    echo "127.0.0.1 rwx" >${REDIS_HOME}/whitelist/whitelist.ip
    mkdir -p ${REDIS_HOME}/data
    mkdir -p ${REDIS_HOME}/log
    mkdir -p ${REDIS_HOME}/conf
    mkdir -p ${REDIS_HOME}/var

    # sentinel
    # 目录不为空，替换bin
    mkdir -p ${SENTINEL_HOME}/bin/ && mv ${SOFT_DIR}/local/sentinel/bin/* ${SENTINEL_HOME}/bin/
    # 替换启动脚本
    mv ${SOFT_DIR}/local/sentinel/sentinel_service.sh ${REDIS_DIR}/local/sentinel/
    # 生成白名单目录
    mkdir -p ${SENTINEL_HOME}/whitelist && touch ${SENTINEL_HOME}/whitelist/whitelist.ip && touch ${SENTINEL_HOME}/whitelist/whitelist.bns
    echo "127.0.0.1 rwx" >${SENTINEL_HOME}/whitelist/whitelist.ip
    mkdir -p ${SENTINEL_HOME}/log
    mkdir -p ${SENTINEL_HOME}/conf

    # router
    # 目录不为空，替换bin
    mkdir -p ${REDIS_DIR}/local/router/bin/ && mv ${SOFT_DIR}/local/router/bin/* ${REDIS_DIR}/local/router/bin/
    # 替换启动脚本
    mv ${SOFT_DIR}/local/router/nutcracker_service.sh ${REDIS_DIR}/local/router/
    # 生成白名单目录
    mkdir -p ${ROUTER_HOME}/whitelist && touch ${ROUTER_HOME}/whitelist/whitelist.ip && touch ${ROUTER_HOME}/whitelist/whitelist.bns
    echo "127.0.0.1 rwx" >${ROUTER_HOME}/whitelist/whitelist.ip
    # 创建coresave目录
    mkdir -p ${ROUTER_HOME}/log
    mkdir -p ${ROUTER_HOME}/conf
    mkdir -p ${ROUTER_HOME}/var

    # 创建coresave目录
    mkdir -p ${REDIS_DIR}/coresave
    #初始化容器环境 bashrc
    cat >${REDIS_DIR}/.bashrc <<EOF
# .bashrc

# Source global definitions
if [ -f /etc/bashrc ]; then
    . /etc/bashrc
fi

# Uncomment the following line if you don't like systemctl's auto-paging feature:
# export SYSTEMD_PAGER=

# User specific aliases and functions
PS1='[\[\e[31m\]\u\[\e[m\]@\[\e[32m\]\h\[\e[m\]:\[\e[35m\]docker\[\e[m\] \[\e[33m\]\w\[\e[m\]]$ '
EOF
    # 初始化 bash_profile
    cat >/home/<USER>/.bash_profile <<EOF
# .bash_profile

# Get the aliases and functions
if [ -f ~/.bashrc ]; then
    . ~/.bashrc
fi

# User specific environment and startup programs

PATH=$PATH:$HOME/.local/bin:$HOME/bin

export PATH
EOF
}

readonly max_retries=2

term_handler() {
    if [ $pid -ne 0 ]; then
        kill -SIGTERM "$pid"
        wait "$pid"
    fi
    # 128 + 15 -- SIGTERM
    exit 143
}

if [ x"$1" == x"run" ]; then

    trap 'kill ${!}; term_handler' SIGTERM

    cd ${REDIS_DIR}

    init_env
    conf_sed

    bin_file=${REDIS_HOME}/bin/redis-server
    ${bin_file} ${REDIS_HOME}/conf/redis.conf &

    bin_file=${SENTINEL_HOME}/bin/redis-sentinel
    ${bin_file} ${SENTINEL_HOME}/conf/sentinel.conf &
    sleep 5
    ${SENTINEL_HOME}/bin/redis-cli -p ${SENTINEL_PORT} SENTINEL MONITOR test-server1 127.0.0.1 ${REDIS_PORT} 1

    bin_file=${ROUTER_HOME}/bin/nutcracker
    ${bin_file} -c ${ROUTER_HOME}/conf/nutcracker.yml -o ${ROUTER_HOME}/log/nutcracker.log -A 127.0.0.1 -S ${SENTINEL_PORT} -s ${STAT_PORT} -v 4 -m 16384 -w ${ROUTER_HOME}/whitelist/whitelist -p ${ROUTER_HOME}/var/nutcracker.pid &
    sleep 5
    if [ ! -z "${CLIENT_AUTH}" ]; then
        ${SENTINEL_HOME}/bin/redis-cli -a ${CLIENT_AUTH} -p ${ROUTER_PORT} "turnoff"
    else
        ${SENTINEL_HOME}/bin/redis-cli -p ${ROUTER_PORT} "turnoff"
    fi
    while true; do
        tail -f /dev/null
    done
else
    conf_sed
    exec "$@"
fi
