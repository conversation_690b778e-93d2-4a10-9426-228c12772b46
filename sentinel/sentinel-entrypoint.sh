#!/bin/bash
set -x

pid=0
readonly APP_HOME="/home/<USER>/local/sentinel"
readonly APP_INSTALL="/home/<USER>/local/install"
readonly REDIS_DIR="/home/<USER>"
readonly SOFT_DIR="/home/<USER>"

# 检查cmanager地址环境变量是否存在
if [ -z "$CMANAGER_URL" ]; then
    # 如果cmanager地址环境变量不存在，则使用默认值
    CMANAGER_URL="redis-cmanager.siod-redis.serv:6360"
fi

# 检查cmanager TOKEN 环境变量是否存在
if [ -z "$CMANAGER_TOKEN" ]; then
    # 如果cmanager TOKEN 环境变量不存在，则使用默认值
    CMANAGER_TOKEN="ST8ffkYhbBghFBdbKdvVhRz6"
fi

conf_sed() {
    cp ${APP_INSTALL}/sentinel_template.conf ${APP_HOME}/conf/sentinel.conf
    sed -i "s/SENTINEL_PORT/${SENTINEL_PORT}/g" ${APP_HOME}/conf/sentinel.conf
    sed -i "s#APP_HOME#${APP_HOME}#g" ${APP_HOME}/conf/sentinel.conf
}

init_env() {
    # 目录不为空，替换bin
    mkdir -p ${APP_HOME}/bin/ && mv ${SOFT_DIR}/local/sentinel/bin/* ${APP_HOME}/bin/
    # 替换启动脚本
    mv ${SOFT_DIR}/local/sentinel/sentinel_service.sh ${REDIS_DIR}/local/sentinel/
    # 生成白名单目录
    mkdir -p ${REDIS_DIR}/whitelist && touch ${REDIS_DIR}/whitelist/whitelist.ip && touch ${REDIS_DIR}/whitelist/whitelist.bns
    # 创建coresave目录
    mkdir -p ${REDIS_DIR}/coresave
    #初始化容器环境 bashrc
    cat >${REDIS_DIR}/.bashrc <<EOF
# .bashrc

# Source global definitions
if [ -f /etc/bashrc ]; then
    . /etc/bashrc
fi

# Uncomment the following line if you don't like systemctl's auto-paging feature:
# export SYSTEMD_PAGER=

# User specific aliases and functions
PS1='[\[\e[31m\]\u\[\e[m\]@\[\e[32m\]\h\[\e[m\]:\[\e[35m\]docker\[\e[m\] \[\e[33m\]\w\[\e[m\]]$ '
EOF
    # 初始化 bash_profile
    cat >/home/<USER>/.bash_profile <<EOF
# .bash_profile

# Get the aliases and functions
if [ -f ~/.bashrc ]; then
    . ~/.bashrc
fi

# User specific environment and startup programs

PATH=$PATH:$HOME/.local/bin:$HOME/bin

export PATH
EOF
}

readonly max_retries=2

init_redis_agent() {
    # 获取redis-agent包并移动目录
    url="http://${CMANAGER_URL}/redis-cmanager/cluster/pod/agentPackage"
    http_response="" # 初始为空，将通过链接获取
    file_to_download="output.tgz"
    get_http_response
    download_file
    tar zxf output.tgz && mv output ${REDIS_DIR}/redis-agent && rm -rf output*

    # 获取redis-agent配置文件并移动目录
    url="http://${CMANAGER_URL}/redis-cmanager/cluster/pod/agentConfig"
    http_response="" # 初始为空，将通过链接获取
    get_http_response
    echo "$http_response" >config.yaml
    mv config.yaml ${REDIS_DIR}/redis-agent/config/

    # 启动agent
    ${REDIS_DIR}/redis-agent/control.sh start

    exit_code=$?

    # 检查退出状态码
    if [ $exit_code -ne 0 ]; then
        echo "脚本 ${REDIS_DIR}/redis-agent/control.sh 执行失败，退出状态码为 $exit_code"
        exit $exit_code
    fi
}

init_redis_monitor() {
    # 获取redis-agent包并移动目录
    url="http://${CMANAGER_URL}/redis-cmanager/cluster/pod/redisMonitorPackage"
    http_response="" # 初始为空，将通过链接获取
    file_to_download="output.tgz"
    get_http_response
    download_file
    tar zxf output.tgz && mv output/docker/redis_monitor ${REDIS_DIR}/redis_monitor && rm -rf output*
}

# 获取HTTP地址
get_http_response() {
    for ((i = 1; i <= max_retries; i++)); do
        echo "尝试 #$i 获取HTTP响应..."
        http_response=$(curl --header "Authorization: ${CMANAGER_TOKEN}" "$url")
        if [ "$http_response" ]; then
            echo "HTTP获取成功：$http_response"
            return 0
        else
            echo "获取HTTP失败，正在重试..."
        fi
    done
    echo "获取HTTP失败超过$max_retries次，脚本将退出。"
    exit 1
}

# 下载文件
download_file() {
    for ((i = 1; i <= max_retries; i++)); do
        echo "尝试 #$i 下载文件..."
        if wget --tries=1 "$http_response" -O "$file_to_download"; then
            echo "文件下载成功。"
            return 0
        elif curl -f -o "$file_to_download" "$http_response"; then
            echo "文件下载成功。"
            return 0
        else
            echo "下载文件失败，正在重试..."
        fi
    done
    echo "下载文件失败超过$max_retries次，脚本将退出。"
    exit 1
}

term_handler() {
    if [ $pid -ne 0 ]; then
        kill -SIGTERM "$pid"
        wait "$pid"
    fi
    # 128 + 15 -- SIGTERM
    exit 143
}

if [ x"$1" == x"run" ]; then
    trap 'kill ${!}; term_handler' SIGTERM

    cd ${REDIS_DIR}

    init_env
    init_redis_agent
    init_redis_monitor

    mkdir -p ${APP_HOME}/log
    mkdir -p ${APP_HOME}/conf

    conf_sed
    bin_file=${APP_HOME}/bin/redis-sentinel
    ${bin_file} ${APP_HOME}/conf/sentinel.conf &

    pid="$!"
    while true; do
        tail -f /dev/null &
        wait ${!}
    done
else
    conf_sed
    exec "$@"
fi
