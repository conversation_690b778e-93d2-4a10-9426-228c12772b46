#!/bin/bash
# 用于启动router 的shell脚本.
# author: na<PERSON><PERSON><PERSON><PERSON><PERSON>@duxiaoman.com 2023.03.09

# app的bin文件名，需要放置在${APP_HOME}/bin/目录下
readonly APP_HOME="$(
    cd "$(dirname "$0")"
    pwd
)"
bin_name="redis-sentinel"
bin_file="${APP_HOME}/bin/${bin_name}"
# pid的绝对路径
pid_file="${APP_HOME}/var/redis.pid"
sentinel_conf="${APP_HOME}/conf/sentinel.conf"
port=$(grep "^port " ${sentinel_conf} | awk '{print $2}')

# pid的name
pid_bin_name=0

# 通过ps命令获取pid
function get_app_pid_by_psef() {
    pid_bin_name="$(ps -ef | grep ${APP_HOME}/bin/${bin_name} | grep :${port} | grep -v grep | awk '{print $2}' | head -n 1)"
}
# 启动进程
function start() {
    get_app_pid_by_psef

    # 1.判断进程是否已经启动
    if [[ ! -z "${pid_bin_name}" ]]; then
        echo "[${bin_name} already started!](pid=${pid_bin_name} port=${port})"
        exit 0
    fi

    # 2.启动sentinel实例
    ${bin_file} ${sentinel_conf}
    sleep 1

    # 3.通过ps -ef查找app的pid，检查启动是否成功
    get_app_pid_by_psef
    if [ "${pid_bin_name}" != "" ]; then
        echo "[${bin_name} start success!] (pid=${pid_bin_name} port=${port})"
        exit 0
    else
        echo "[${bin_name} start failed!]"
        exit 1
    fi
}
# 关闭进程
function stop() {
    # 1.通过ps -ef方式获取pid
    get_app_pid_by_psef
    stop_bin_pid=${pid_bin_name}
    # 2.判断pid是否存在并进行kill操作
    ${APP_HOME}/bin/redis-cli -p ${port} "shutdown"
    get_app_pid_by_psef
    if [[ "${pid_bin_name}" == "" ]]; then
        echo "[${bin_name} stop success!](pid=${stop_bin_pid} port=${port})"
    else
        echo "[${bin_name} stop failed!](pid=${pid_bin_name} port=${port})"
    fi
}
#状态
function status() {
    get_app_pid_by_psef
    if [[ "${pid_bin_name}" != "" ]]; then
        echo -e "\033[32m [${bin_name} is running!](pid=${pid_bin_name} port=${port}) \033[0m"
    else
        echo -e "\033[31m [${bin_name} is not running!](pid=${pid_bin_name} port=${port}) \033[0m"
    fi
}
# usage
function usage() {
    echo "Usage: $0 {start|stop|status|restart}"
}

function main() {
    case "$1" in
    'start')
        start
        ;;
    'stop')
        stop
        ;;
    'status')
        status
        ;;
    'restart')
        stop
        start
        ;;
    '-h')
        usage
        ;;
    *)
        usage
        exit 1
        ;;
    esac
    exit 0
}

# 7. 主函数/主逻辑
main "$@"
