#!/usr/bin/env python
# -*- coding:utf-8 -*-
import sys
import os
import time

basedir = os.path.abspath(os.path.dirname(sys.argv[0]))
import re
import traceback
import commands
import time
import signal
import json
from collections import namedtuple

# Number of clock ticks per second
CLOCK_TICKS = os.sysconf("SC_CLK_TCK")
PAGESIZE = os.sysconf("SC_PAGE_SIZE")
pcputimes = namedtuple('pcputimes', ['user', 'system', 'children_user', 'children_system', 'iowait'])
pmem = namedtuple('pmem', 'rss vms shared text lib data dirty')
# 设置执行超时时间（秒）
MAX_EXECUTION_TIME = 10

def timeout_handler(signum, frame):
    print("执行超时")
    exit(255)


def pids():
    """Returns a list of PIDs currently running on the system."""
    return [int(x) for x in os.listdir("/proc") if x.isdigit()]


class Process(object):
    """此部分截取自psutil工具
    """

    def __init__(self, pid):
        self.pid = pid
        self.name = ""

    def exe(self):
        try:
            self.exe = self._readlink("/proc/%s/exe" % (self.pid))
        except Exception, e:
            self.exe = ""
        return self.exe

    def cwd(self):
        try:
            self.cwd = self._readlink("/proc/%s/cwd" % (self.pid))
        except Exception, e:
            self.cwd = ""
        return self.cwd

    def cmdline(self):
        try:
            self.cmdline = commands.getoutput("cat /proc/%s/cmdline" % (self.pid))
        except Exception, e:
            self.cmdline = ""
        return self.cmdline

    def _parse_stat_file(self):
        """Parse /proc/{pid}/stat file and return a dict with various
        process info.
        Using "man proc" as a reference: where "man proc" refers to
        position N always substract 3 (e.g ppid position 4 in
        'man proc' == position 1 in here).
        The return value is cached in case oneshot() ctx manager is
        in use.
        """
        with open("/proc/%s/stat" % (self.pid), "rb") as f:
            data = f.read()
        # Process name is between parentheses. It can contain spaces and
        # other parentheses. This is taken into account by looking for
        # the first occurrence of "(" and the last occurence of ")".
        rpar = data.rfind(b')')
        self.name = data[data.find(b'(') + 1:rpar]
        fields = data[rpar + 2:].split()
        ret = {}
        ret['name'] = self.name
        ret['status'] = fields[0]
        ret['ppid'] = fields[1]
        ret['ttynr'] = fields[4]
        ret['utime'] = fields[11]
        ret['stime'] = fields[12]
        ret['children_utime'] = fields[13]
        ret['children_stime'] = fields[14]
        ret['create_time'] = fields[19]
        ret['cpu_num'] = fields[36]
        ret['blkio_ticks'] = fields[39]  # aka 'delayacct_blkio_ticks'
        self.stat_values = ret

    def cpu_times(self):
        self._parse_stat_file()
        utime = float(self.stat_values['utime']) / CLOCK_TICKS
        stime = float(self.stat_values['stime']) / CLOCK_TICKS
        children_utime = float(self.stat_values['children_utime']) / CLOCK_TICKS
        children_stime = float(self.stat_values['children_stime']) / CLOCK_TICKS
        iowait = float(self.stat_values['blkio_ticks']) / CLOCK_TICKS
        return pcputimes(utime, stime, children_utime, children_stime, iowait)

    def memory_info(self):
        #  ============================================================
        # | FIELD  | DESCRIPTION                         | AKA  | TOP  |
        #  ============================================================
        # | rss    | resident set size                   |      | RES  |
        # | vms    | total program size                  | size | VIRT |
        # | shared | shared pages (from shared mappings) |      | SHR  |
        # | text   | text ('code')                       | trs  | CODE |
        # | lib    | library (unused in Linux 2.6)       | lrs  |      |
        # | data   | data + stack                        | drs  | DATA |
        # | dirty  | dirty pages (unused in Linux 2.6)   | dt   |      |
        #  ============================================================
        with open("/proc/%s/statm" % (self.pid), "rb") as f:
            vms, rss, shared, text, lib, data, dirty = \
                [int(x) * PAGESIZE for x in f.readline().split()[:7]]
        return pmem(rss, vms, shared, text, lib, data, dirty)

    def _readlink(self, path):
        """Wrapper around os.readlink()."""
        assert isinstance(path, basestring), path
        path = os.readlink(path)
        # readlink() might return paths containing null bytes ('\x00')
        # resulting in "TypeError: must be encoded string without NULL
        # bytes, not str" errors when the string is passed to other
        # fs-related functions (os.*, open(), ...).
        # Apparently everything after '\x00' is garbage (we can have
        # ' (deleted)', 'new' and possibly others), see:
        # https://github.com/giampaolo/psutil/issues/717
        path = path.split('\x00')[0]
        # Certain paths have ' (deleted)' appended. Usually this is
        # bogus as the file actually exists. Even if it doesn't we
        # don't care.
        if path.endswith(' (deleted)') and not path_exists_strict(path):
            path = path[:-10]
        return path

    def get_ppid(self):
        try:
            with open_binary("/proc/%s/stat" % (self.pid)) as f:
                data = f.read()
        except Exception, e:
            # Note: we should be able to access /stat for all processes
            # aka it's unlikely we'll bump into EPERM, which is good.
            return 0
        rpar = data.rfind(b')')
        dset = data[rpar + 2:].split()
        ppid = int(dset[1])
        return ppid


class Resource(object):
    # 资源采集
    def __init__(self, instance=""):
        # 初始化
        self.resource_data = {}
        self.resource_data["cpu"] = {}
        self.resource_data["mem"] = {}
        self.resource_data["monitor"] = []
        self.resource_data["error"] = []
        self.debug = 0
        self.noah_agent_docker_id = ""
        self.debug_info = []
        self.instance_id = instance
        self.output = {}
        self.ppid = os.getppid()
        # instance id获取前默认值,防止instance_id获取失败出现未赋值情况
        self.data = "%s/.default"
        self.check_instance_id()
        # 在脚本路径下，使用隐藏文件存储数据信息
        self.basedir = os.path.abspath(os.path.dirname(sys.argv[0]))
        self.data = "%s/.%s" % (self.basedir, self.instance_id)
        self.load()
        self.type = "docker"
        if self.has_docker():
            if self.get_docker_env():
                self.set_type("docker")
        else:
            print("not have docker")
            exit(255)

    def check_instance_id(self):
        if self.instance_id == "":
            print("instance_id must not empty")
            exit(255)

    def set_type(self, typestr):
        # 设置类型，物理机还是容器
        self.type = typestr
        self.resource_data["type"] = self.type

    def load(self):
        # 从本地读取上次采集数据，如果没有数据创建数据记录文件
        try:
            with open(self.data, 'r') as f:
                self.resource_history = json.load(f)
            return
        except Exception, e:
            self.resource_history = {}
            with open(self.data, 'w') as fw:
                pass
            return

    def has_docker(self):
        # 判断是否安装了docker
        docker_sock = "/home/<USER>/docker/docker_dxm/data/docker.sock"
        if os.path.exists(docker_sock):
            return True
        docker_sock = "/home/<USER>/docker_dxm/data/docker.sock"
        if os.path.exists(docker_sock):
            return True
        return False

    def __del__(self):
        # 结束时自动写入历史数据
        with open(self.data, 'w') as fw:
            json.dump(self.resource_data, fw)
        # 设定写权限，避免监控账号切换导致无写权限失效
        os.chmod(self.data, 0666)
        if self.debug == 1:
            f = open("%s/dump/%s_dump" % (basedir, self.instance_id), "w")
            for p in self.debug_info:
                f.write("%s %s %s\n" % (p.pid, p.name, p.cmdline()))
            f.close()

    def error(self, msg):
        # 收集异常信息
        self.resource_data["error"].append(msg)

    def cmd_exec(self, cmd, timeout=1):

        # 命令执行
        (status, output) = commands.getstatusoutput("%s" % (cmd))
        if status != 0:
            if status == 124:
                self.error("%s executed time out!" % cmd)
            else:
                self.error("cmd:%s errno:%s errmsg:%s" % (cmd, status, output))
        return status, output

    def get_phy_conf(self):
        # 获取物理机环境配置信息
        data = "%s/conf/%s" % (basedir, self.instance_id)
        try:
            with open(data, 'r') as f:
                env = json.load(f)
                self.path = env["deploy_path"]
                if self.path[-1] == "/":
                    self.path = self.path[:-1]
                self.mem_quota_mb = env["mem_quota_mb"]
                self.cpu_quota = env["cpu_quota"]
                self.debug = env["debug"]
        except Exception, e:
            self.error(traceback.print_exc())
            sys.exit(0)

    def get_monitor_container_id(self):
        (status, output) = self.cmd_exec(
            "docker -H unix:///home/<USER>/docker/docker_dxm/data/docker.sock ps -f label=io.kubernetes.sandbox.id=%s |tail -n +2|awk '{print $1}'" % (
                self.docker_short_id))
        redis_container_exist = False
        noah_container_exist = False
        for instance_id in output.split('\n'):
            (status, output) = self.cmd_exec(
                "docker -H unix:///home/<USER>/docker/docker_dxm/data/docker.sock inspect %s" % instance_id)
            # 获取每个相同sandbox.id的容器信息，获取需要监控的容器的容器id和noah-agent的容器id
            if status == 0:
                docker_info = json.loads(output)
                docker_info_path = docker_info[0]["Path"]
                docker_info_label = docker_info[0]["Config"]["Labels"]
                if (docker_info_path == "/home/<USER>/local/install/router-entrypoint.sh" or
                        docker_info_path == "/home/<USER>/local/install/redis-entrypoint.sh" or
                        docker_info_path == "/home/<USER>/local/install/sentinel-entrypoint.sh"):
                    self.docker_short_id = instance_id
                    redis_container_exist = True
                if docker_info_label["io.kubernetes.container.name"] == "noah-agent":
                    self.noah_agent_docker_id = instance_id
                    noah_container_exist = True
            else:
                self.error("get docker info failed! %s" % (output))
                return False
        # 若需要监控的容器和noah-agent容器都存在，则进行处理
        if redis_container_exist and noah_container_exist:
            return True
        else:
            return False
    def get_docker_env(self):
        # 获取容器环境配置信息
        os.putenv("PATH",
                  "/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin:/root/bin:/home/<USER>/docker_dxm/bin:/home/<USER>/docker/docker_dxm/bin")

        # docker short id length is 12
        # if len(output) == 12:
        self.docker_short_id = self.instance_id
        # else:
        #     self.error("get docker id failed! %s" % self.instance_id)
        #     return False

        (status, output) = self.cmd_exec(
            "docker -H unix:///home/<USER>/docker/docker_dxm/data/docker.sock inspect %s" % self.docker_short_id)
        if status == 0:
            self.docker_info = json.loads(output)[0]
        else:
            self.error("get docker info failed! %s" % (output))
            return False
        # 适配/sys/fs/cgroup和/cgroup两种cgroup路径
        cg_path = "/sys/fs/cgroup"
        if not os.path.exists("%s/cpu" % cg_path):
            cg_path = "/cgroup"
        self.cpu_cgroup_path = "%s/cpu/docker/%s" % (cg_path, self.docker_info["Id"])
        self.mem_cgroup_path = "%s/memory/docker/%s" % (cg_path, self.docker_info["Id"])
        self.blk_cgroup_path = "%s/blkio/docker/%s" % (cg_path, self.docker_info["Id"])

        # 若为k8s的pause容器，则更新dockerShortId,并更新dockerInfo
        if self.docker_info["Path"] == "/pause":
            if not self.get_monitor_container_id():
                return False
            (status, output) = self.cmd_exec(
                "docker -H unix:///home/<USER>/docker/docker_dxm/data/docker.sock inspect %s" % self.docker_short_id)
            if status == 0:
                self.docker_info = json.loads(output)[0]
            else:
                self.error("get k8s docker info failed! %s inspect=[%s]" % (output, self.docker_short_id))
                return False
            self.cpu_cgroup_path = "%s/cpu%s/%s" % (
                cg_path, self.docker_info["HostConfig"]["CgroupParent"], self.docker_info["Id"])
            self.mem_cgroup_path = "%s/memory%s/%s" % (
                cg_path, self.docker_info["HostConfig"]["CgroupParent"], self.docker_info["Id"])
            self.blk_cgroup_path = "%s/blkio%s/%s" % (
                cg_path, self.docker_info["HostConfig"]["CgroupParent"], self.docker_info["Id"])
        return True

    def get_status(self):
        # 统计资源使用率
        self.get_container_status()
        self.get_pid_status()
        if self.type == "phy":
            self.get_phy_status()
        elif self.type == "docker":
            self.get_docker_status()
        # 写入挂载pod的目录中
        self.format_output()

    def get_container_status(self):
        # 获取容器状态
        # 物理机:     -1
        # Running:     0
        # Paused:      1
        # Restarting:  2
        # OOMKilled:   3
        # Dead:        4
        # 未知容器状态:999
        state_dict = {
            "Running": 0,
            "Paused": 1,
            "Restarting": 2,
            "OOMKilled": 3,
            "Dead": 4
        }
        self.output["CONTAINER_STATUS"] = -1
        if self.type == "phy":
            return
        self.output["CONTAINER_STATUS"] = 999
        if "State" not in self.docker_info.keys():
            return
        for key in self.docker_info["State"].keys():
            if self.docker_info["State"][key] == True and key in state_dict.keys():
                self.output["CONTAINER_STATUS"] = state_dict[key]
                return

    def get_docker_status(self):
        # 获取容器的各项资源使用
        self.get_docker_cpu_used()
        self.get_docker_mem_used()

    def get_docker_cpu_used(self):
        # 获取容器CPU使用情况
        self.get_docker_cpu_quota()
        cpuacct_usage = int(commands.getoutput("cat %s/cpuacct.usage" % (self.cpu_cgroup_path)))
        cpuacct_timestamp = int(time.time() * 1000 * 1000)
        self.resource_data["cpu"][self.docker_short_id] = {}
        self.resource_data["cpu"][self.docker_short_id]["cpuacct_usage"] = cpuacct_usage
        self.resource_data["cpu"][self.docker_short_id]["cpuacct_timestamp"] = cpuacct_timestamp
        try:
            cpuacct_usage_history = int(self.resource_history["cpu"][self.docker_short_id]["cpuacct_usage"])
            cpuacct_timestamp_history = int(self.resource_history["cpu"][self.docker_short_id]["cpuacct_timestamp"])
            # 处理因时间戳单位调整导致的负数
            if cpuacct_usage_history != 0 & cpuacct_timestamp_history != 0:
                if cpuacct_timestamp < cpuacct_timestamp_history or cpuacct_timestamp > 10 * cpuacct_timestamp_history:
                    return
            # cgroup中cpu使用时间单位是纳秒，时间戳的单位是微秒，因此在结果上除以10^3
            cpu_used = 100 * (cpuacct_usage - cpuacct_usage_history) / (
                    cpuacct_timestamp - cpuacct_timestamp_history) / 1000
            self.output["CPU_USED"] = cpu_used
        except Exception, e:
            # 无上次CPU使用信息，无法计算CPU使用情况
            self.error("no history data!")
            return
        monitor_cpu = 0
        for pid in self.resource_data["monitor"]:
            if pid not in self.resource_history["cpu"].keys():
                continue
            if pid in self.resource_data["monitor"]:
                monitor_cpu = monitor_cpu + 100 * 1000000 * (
                        self.resource_data["cpu"][pid]["usage"] - self.resource_history["cpu"][pid]["usage"]) / (
                                      self.resource_data["cpu"][pid]["timestamp"] -
                                      self.resource_history["cpu"][pid]["timestamp"])
        self.output["MONITOR_CPU_USED"] = monitor_cpu
        self.output["CPU_USED"] += monitor_cpu
        if float(self.cpu_quota) > 0:
            self.output["CPU_USED_PERCENT"] = self.output["CPU_USED"] / self.cpu_quota

    def get_docker_cpu_quota(self):
        # 获取实例CPU套餐
        cpu_cfs_quota_us = commands.getoutput("cat %s/cpu.cfs_quota_us" % (self.cpu_cgroup_path))
        cpu_cfs_period_us = commands.getoutput("cat %s/cpu.cfs_period_us" % (self.cpu_cgroup_path))
        if int(cpu_cfs_quota_us) > 0:
            self.cpu_quota = float(cpu_cfs_quota_us) / float(cpu_cfs_period_us)
        elif "CPU_QUOTA" in self.docker_info["Config"]["Labels"].keys():
            self.cpu_quota = self.docker_info["Config"]["Labels"]["com.dxm.resource.cpu_quota"]
        else:
            self.cpu_quota = -1
        self.output["CPU_QUOTA"] = self.cpu_quota

    def get_docker_mem_used(self):
        # 获取容器内存使用情况
        self.get_docker_mem_quota()
        memory_cgroup_usage_mb = int(
            commands.getoutput("cat %s/memory.usage_in_bytes" % (self.mem_cgroup_path))) / 1024 / 1024
        self.output["MEM_CGROUP_USED_MB"] = memory_cgroup_usage_mb
        memory_cache_mb = int(
            commands.getoutput("cat {}/memory.stat|grep '^cache '".format(self.mem_cgroup_path)).split(" ")[
                1]) / 1024 / 1024
        self.output["MEM_CACHE_MB"] = memory_cache_mb
        memory_rss_mb = int(
            commands.getoutput("cat {}/memory.stat|grep '^rss '".format(self.mem_cgroup_path)).split(" ")[
                1]) / 1024 / 1024
        for pid in self.resource_data["monitor"]:
            if not self.resource_history.has_key("mem") or pid not in self.resource_history["mem"].keys():
                continue
            if pid in self.resource_data["monitor"]:
                memory_rss_mb += self.resource_data["mem"][pid]["rss"] / 1024 / 1024
        memory_used_percent = 100 * memory_rss_mb / float(self.memory_quota)
        self.output["MEM_USED_MB"] = memory_rss_mb
        self.output["MEM_USED_PERCENT"] = memory_used_percent
        memory_failcnt = int(commands.getoutput("cat %s/memory.failcnt" % (self.mem_cgroup_path)))
        self.resource_data["mem"]["memory_failcnt"] = memory_failcnt
        try:
            memory_failcnt_history = int(self.resource_history["mem"]["memory_failcnt"])
            self.output["MEM_FAIL_CNT"] = memory_failcnt - memory_failcnt_history
        except Exception, e:
            # 历史信息获取失败，不进行计算
            self.error("no history data!")
            return

    def get_docker_mem_quota(self):
        # 获取实例内存套餐
        self.memory_quota = int(
            commands.getoutput("cat %s/memory.limit_in_bytes" % (self.mem_cgroup_path))) / 1024 / 1024
        # 常数值设定来源参考:https://unix.stackexchange.com/questions/420906/what-is-the-value-for-the-cgroups-limit-in-bytes-if-the-memory-is-not-restricte
        # 9223372036854771712 is 0x7FFFFFFFFFFFF000
        # The value is the maximum 64-bit signed integer, rounded to the nearest page (by dropping the last bits).
        # if self.memory_quota == 9223372036854771712/1024/1024:
        # if "com.dxm.resource.mem_quota_byte" in self.docker_info["Config"]["Labels"].keys():
        # self.memory_quota = -1
        self.output["MEM_QUOTA_MB"] = self.memory_quota

    def get_used_by_process(self, p):
        ##统计进程的cpu 内存使用信息
        self.debug_info.append(p)
        cpu = p.cpu_times()
        key = str(p.pid)
        if "cpu" not in self.resource_data.keys():
            self.resource_data["cpu"] = {}
        if "mem" not in self.resource_data.keys():
            self.resource_data["mem"] = {}

        self.resource_data["cpu"][key] = {}
        self.resource_data["cpu"][key]["timestamp"] = int(time.time() * 1000 * 1000)
        self.resource_data["cpu"][key]["usage"] = cpu.user + cpu.system
        self.resource_data["cpu"][key]["usage_user"] = cpu.user
        self.resource_data["cpu"][key]["usage_sys"] = cpu.system
        self.resource_data["mem"][key] = {}
        self.resource_data["mem"][key]["rss"] = p.memory_info().rss
        return

    def get_pid_status(self):
        # 统计进程资源使用率
        # 找到全部进程
        pidlist = pids()
        for pid in pidlist:
            try:
                p = Process(pid)
                if pid == self.ppid or p.get_ppid() == self.ppid:
                    self.get_used_by_process(p)
                    if "monitor" not in self.resource_data.keys():
                        self.resource_data["monitor"] = []
                    self.resource_data["monitor"].append(str(pid))
                # 容器化实例不用统计监控以外进程使用率
                if self.type == "docker":
                    continue
                # 物理机进程识别采用进程路径或者工作路径进行识别
                if self.is_path_matched(p.exe()) or self.is_path_matched(p.cwd()):
                    self.get_used_by_process(p)
            except Exception:
                self.error(traceback.format_exc())
                continue

    def get_mount_path(self):
        # 获取挂载目录地址
        (status, output) = self.cmd_exec(
            "docker -H unix:///home/<USER>/docker/docker_dxm/data/docker.sock inspect -f '{{json .Mounts}}' %s" % self.noah_agent_docker_id)
        mounts = json.loads(output)
        # 获取noah-agent容器pv的物理机路径，用于存储podInfo
        for mount in mounts:
            if mount["Destination"] == "/home/<USER>":
                return mount["Source"]

    def format_output(self):
        pvdir = self.get_mount_path()
        if pvdir is None:
            pvdir = "."
        # 格式化输出写入挂载地址
        data = ""
        podInfo = "%s/podInfo" % pvdir
        for key in self.output:
            value = self.output[key]
            if isinstance(value, int):
                data = "%s%s: %d\n" % (data, key, value)
            else:
                data = "%s%s: %.2f\n" % (data, key, value)
        with open(podInfo, 'w') as file:
            file.write(data)

    def is_path_matched(self, pid_dir):
        # 两种匹配，完全相同匹配和前缀匹配
        if self.path == pid_dir:
            return True
        # 前缀匹配需要补"/",以确保匹配的都是该目录下的路径
        if len(self.path) < len(pid_dir):
            if self.path + "/" == pid_dir[:len(self.path) + 1]:
                return True
        return False

    def get_phy_status(self):
        # 获取物理机资源使用情况
        mem = 0
        for pid in self.resource_data["mem"]:
            mem += self.resource_data["mem"][pid]["rss"]
        self.output["MEM_USED_MB"] = mem / 1024 / 1024
        self.output["MEM_QUOTA_MB"] = self.mem_quota_mb
        self.output["MEM_USED_PERCENT"] = 100 * mem / 1024.0 / 1024 / self.mem_quota_mb

        app_cpu = 0
        app_cpu_user = 0
        app_cpu_sys = 0
        monitor_cpu = 0
        for pid in self.resource_data["cpu"]:
            # 无历史数据的进行跳过
            if pid not in self.resource_history["cpu"].keys():
                continue
            if pid in self.resource_data["monitor"]:
                # cpu使用时间的单位是秒，时间戳的单位是微秒，因此需要乘以10^6
                monitor_cpu = monitor_cpu + 100 * 1000000 * (
                        self.resource_data["cpu"][pid]["usage"] - self.resource_history["cpu"][pid]["usage"]) / (
                                      self.resource_data["cpu"][pid]["timestamp"] -
                                      self.resource_history["cpu"][pid]["timestamp"])
            else:
                app_cpu = app_cpu + 100 * 1000000 * (
                        self.resource_data["cpu"][pid]["usage"] - self.resource_history["cpu"][pid]["usage"]) / (
                                  self.resource_data["cpu"][pid]["timestamp"] - self.resource_history["cpu"][pid][
                              "timestamp"])
                app_cpu_user = app_cpu_user + 100 * 1000000 * (
                        self.resource_data["cpu"][pid]["usage_user"] - self.resource_history["cpu"][pid][
                    "usage_user"]) / (self.resource_data["cpu"][pid]["timestamp"] -
                                      self.resource_history["cpu"][pid]["timestamp"])
                app_cpu_sys = app_cpu_sys + 100 * 1000000 * (
                        self.resource_data["cpu"][pid]["usage_sys"] - self.resource_history["cpu"][pid][
                    "usage_sys"]) / (self.resource_data["cpu"][pid]["timestamp"] -
                                     self.resource_history["cpu"][pid]["timestamp"])
        self.output["CPU_QUOTA"] = self.cpu_quota
        self.output["APP_CPU_USED"] = app_cpu
        self.output["MONITOR_CPU_USED"] = monitor_cpu
        self.output["CPU_USED_USER"] = app_cpu_user
        self.output["CPU_USED_SYS"] = app_cpu_sys
        self.output["CPU_USED"] = self.output["APP_CPU_USED"] + self.output["MONITOR_CPU_USED"]
        self.output["CPU_USED_PERCENT"] = self.output["CPU_USED"] / self.output["CPU_QUOTA"]


def log_msg(msg, file_name):
    # 获取当前时间戳（秒）
    current_timestamp = time.time()
    # 将时间戳转换为本地时间
    local_time = time.localtime(current_timestamp)
    # 格式化本地时间为字符串
    formatted_time = time.strftime("%Y-%m-%d %H:%M:%S", local_time)

    f = open("%s/.podInfo_%s_error" % (basedir, file_name),  "w")
    f.write("%s %s\n" % (formatted_time, msg))
    f.close()


def exec_cmd(timeout, cmd):
    # 获取容器环境配置信息
    os.putenv("PATH",
              "/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin:/root/bin:/home/<USER>/docker_dxm/bin:/home/<USER>/docker/docker_dxm/bin")
    # 命令执行
    (status, output) = commands.getstatusoutput("%s" % (cmd))
    if status != 0:
        if status == 124:
            print("%s executed time out!" % cmd)
            exit(status)
        else:
            print("cmd:%s errno:%s errmsg:%s" % (cmd, status, output))
            exit(status)
    return status, output


def main():
    # 设置信号处理函数
    signal.signal(signal.SIGALRM, timeout_handler)
    # 设置超时时间
    signal.alarm(MAX_EXECUTION_TIME)
    try:
        # 获取所有app.kubernetes.io/managed-by=redis-operator容器
        (status, output) = exec_cmd(2,
                                    "docker -H unix:///home/<USER>/docker/docker_dxm/data/docker.sock ps -f label=app.kubernetes.io/managed-by=redis-operator --no-trunc -q")
        # 遍历所有容器id
        for instance_id in output.split('\n'):
            try:
                r = Resource(instance_id)
                r.get_status()
            except Exception, e:
                log_msg("get resource failed. %s" % e, instance_id)
                continue
    except Exception, e:
        log_msg("get docker instance id faild. except=[%s]" % e, instance_id)
        log_msg(traceback.format_exc(), instance_id)


if __name__ == "__main__":
    main()