#!/bin/bash

# 获取版本号
version=$(cat ./version)

# 复制版本文件到各个目录
cp ./version ./redis
cp ./version ./sentinel
cp ./version ./router
cp ./version ./all

# build 方法
build() {
    echo "Building Docker images..."
    docker build --network=host -t r.duxiaoman-int.com/siod_redis/redis-docker:v$version ./redis
    docker build --network=host -t r.duxiaoman-int.com/siod_redis/sentinel-docker:v$version ./sentinel
    docker build --network=host -t r.duxiaoman-int.com/siod_redis/router-docker:v$version ./router
    docker build --network=host -t r.duxiaoman-int.com/siod_redis/redis-docker-all:v$version ./all

    docker tag r.duxiaoman-int.com/siod_redis/redis-docker:v$version r.duxiaoman-int.com/siod_redis/redis-docker:latest
    docker tag r.duxiaoman-int.com/siod_redis/sentinel-docker:v$version r.duxiaoman-int.com/siod_redis/sentinel-docker:latest
    docker tag r.duxiaoman-int.com/siod_redis/router-docker:v$version r.duxiaoman-int.com/siod_redis/router-docker:latest
    docker tag r.duxiaoman-int.com/siod_redis/redis-docker-all:v$version r.duxiaoman-int.com/siod_redis/redis-docker-all:latest
}

# push 方法
push() {
    echo "Pushing Docker images..."
    docker push r.duxiaoman-int.com/siod_redis/redis-docker:v$version
    docker push r.duxiaoman-int.com/siod_redis/sentinel-docker:v$version
    docker push r.duxiaoman-int.com/siod_redis/router-docker:v$version
    docker push r.duxiaoman-int.com/siod_redis/redis-docker-all:v$version
    docker push r.duxiaoman-int.com/siod_redis/redis-docker:latest
    docker push r.duxiaoman-int.com/siod_redis/sentinel-docker:latest
    docker push r.duxiaoman-int.com/siod_redis/router-docker:latest
    docker push r.duxiaoman-int.com/siod_redis/redis-docker-all:latest
}

# 根据传入的参数执行对应的方法
if [ "$1" == "build" ]; then
    build
elif [ "$1" == "push" ]; then
    push
else
    echo "Error: Invalid command. Use 'build' or 'push'."
    exit 1
fi


# docker run -d r.duxiaoman-int.com/siod_redis/redis-docker-all:v$version sleep infinity
